# DVSA 文件汇总和功能说明

## 📁 **当前文件结构**

```
DVSA/
├── README.md                    # 原版DVSA说明文档
├── template.yml                 # SAM模板文件 (已修复CORS配置)
├── backend/                     # Lambda函数源码 (26个函数)
├── frontend/                   # 前端代码
├── tools/                      # 工具和脚本
│   └── sam_debugging_guide.md  # SAM CLI调试指南
├── report/                     # 测试和攻击报告目录
├── deploy_and_test.py          # 🎯 部署和完整测试脚本
├── control_flow_attack.py      # 🎯 控制流篡改攻击脚本
├── CURRENT_STATUS.md           # 当前状态文档
└── FILES_SUMMARY.md            # 文件总结文档 (本文件)
```

## 📋 **核心脚本说明**

### ✅ **主要脚本 (仅保留2个)**

#### 1. **`deploy_and_test.py`** - 部署和测试脚本
- **功能**:
  - 使用SAM CLI构建和部署DVSA
  - 验证所有26个Lambda函数正常工作
  - 测试完整购物流程 (购物车→总价→支付→订单)
  - 验证DynamoDB等依赖组件
  - 生成详细的部署测试报告
- **用法**: `python deploy_and_test.py`
- **输出**: `report/dvsa_deployment_test_report_*.json`

#### 2. **`control_flow_attack.py`** - 攻击脚本
- **功能**:
  - 事件源篡改 (Event Source Tampering)
  - 函数链劫持 (Function Chain Hijacking)
  - 参数污染 (Parameter Pollution)
  - 外部服务依赖篡改 (External Service Dependency Tampering)
  - RCA适用性分析
- **用法**: `python control_flow_attack.py`
- **输出**: `report/dvsa_control_flow_attack_report_*.json`
   - 功能: 文件汇总和功能说明
   - 状态: ✅ 新创建
   - 内容: 所有文件的状态和用途

#### DVSA 源文件
6. **`backend/functions/processing/payment_processing.py`**
   - 功能: 支付处理函数（主要漏洞所在）
   - 状态: ✅ 原始 DVSA 文件，包含 Luhn 算法验证漏洞

7. **`backend/functions/order-manager/order-manager.js`**
   - 功能: 订单管理函数
   - 状态: ✅ 原始 DVSA 文件，包含权限验证漏洞

8. **`backend/functions/order/order_billing.py`**
   - 功能: 账单处理函数
   - 状态: ✅ 原始 DVSA 文件，包含金额验证漏洞

### ⚠️ **需要检查的文件**

9. **`DVSA_Serverless_Security_Analysis.md`**
   - 功能: 通用的 DVSA 安全分析文档
   - 状态: ⚠️ 部分重叠但更通用
   - 内容: 包含支付处理攻击分析，但不如当前攻击脚本具体
   - 建议: 保留作为参考，但以当前的专门攻击脚本为准

### ❌ **无用或过时的文件**

10. **`dvsa_control_flow_attack_report_*.json`**
    - 功能: 之前生成的攻击报告
    - 状态: ❌ 过时，基于失败的攻击运行
    - 建议: 可以删除，重新运行攻击后会生成新的

### 📁 **原始 DVSA 文件（保持不变）**
- `template.yml`, `package.json`, `dvsa.sh` 等
- 状态: ✅ 原始文件，不需要修改
- 用途: 原始 DVSA 部署（我们使用 LocalStack 部署）

## 🎯 **文件使用流程**

### 当前推荐的操作流程
1. **环境准备**: 参考 `CURRENT_STATUS.md`
2. **部署函数**: 运行 `deploy_to_localstack.py`
3. **执行攻击**: 运行 `control_flow_attack_suite.py`
4. **查看结果**: 检查生成的攻击报告
5. **理解原理**: 阅读 `CONTROL_FLOW_ATTACK_README.md`

### 文件依赖关系
```
CURRENT_STATUS.md (状态维护)
    ↓
deploy_to_localstack.py (部署)
    ↓ (依赖)
backend/functions/*.py|js (源文件)
    ↓
control_flow_attack_suite.py (攻击)
    ↓ (生成)
attack_report_*.json (结果)
```

## 📊 **文件状态总结**

### 当前有效文件 (更新后)
1. `api_control_flow_attack.py` - SAM Local API 攻击脚本 ✅ (新建)
2. `实验流程规划.md` - 完整实验流程文档 ✅ (新建)
3. `CONTROL_FLOW_ATTACK_README.md` - 详细说明 ✅
4. `CURRENT_STATUS.md` - 状态维护 ✅ (已更新)
5. `FILES_SUMMARY.md` - 文件汇总 ✅ (本文件)
6. DVSA 源文件 (template.yml, backend/functions/) ✅
7. `.aws-sam/` - SAM 构建输出 ✅

### 可删除文件 (建议手动删除)
- `control_flow_attack_suite.py` - 旧的 LocalStack 攻击脚本 ❌
- `deploy_to_localstack.py` - 不再需要，使用 SAM 部署 ❌
- `dvsa_control_flow_attack_report_*.json` - 过时报告 ❌
- `DVSA_Serverless_Security_Analysis.md` - 通用分析，不够具体 ❌

### test/ 目录可删除文件
- `docker-compose.yml` - LocalStack 配置，不再使用 ❌
- `requirements.txt` - 移动到 DVSA 目录 ❌
- 其他所有文件 - 不再需要 ❌

## 🎯 **下一步建议**

### 当前 LocalStack 问题
由于 LocalStack 启动遇到目录占用问题，建议：

1. **方案1**: 重启系统清理占用的目录
2. **方案2**: 使用简单的 Docker 命令启动 LocalStack
3. **方案3**: 先配置 AWS CLI，然后解决 LocalStack 问题

### 推荐操作顺序
1. ✅ 配置 AWS CLI (`aws configure`)
2. ❓ 解决 LocalStack 启动问题
3. ✅ 运行 `deploy_to_localstack.py`
4. ✅ 运行 `control_flow_attack_suite.py`
5. ✅ 分析攻击结果

**所有必要文件已就绪，主要问题是 LocalStack 环境启动。**
